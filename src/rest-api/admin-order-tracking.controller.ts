import {
  BadRequestException,
  Body,
  Controller,
  Get,
  InternalServerErrorException,
  Param,
  Post,
  Query,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { AdminOrderTrackingService } from './admin-order-tracking.service';
import { AdminOrdersListQueryParams } from './dtos/admin-order-list-param.dto';
import {
  ManualOrderConsumptionDto,
  ManualOrderConsumptionResponseDto,
} from './dtos/manual-order-consumption.dto';
import {
  UpdateOrderReviewDto,
  UpdateOrderReviewResponseDto,
} from './dtos/update-order-review.dto';
import { ApiKeyGuard } from '../shared/guards/api-key.guard';

@Controller('order-tracking/api/v1/admin/orders')
export class AdminOrderTrackingController {
  constructor(
    private readonly adminOrderTrackingService: AdminOrderTrackingService,
  ) {}

  @Get()
  async getAllOrders(
    @Query(new ValidationPipe({ transform: true }))
    queryParams: AdminOrdersListQueryParams,
  ): Promise<any> {
    try {
      return await this.adminOrderTrackingService.getAllOrders(queryParams);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      console.error('Error in getAllOrders:', error);
      throw new InternalServerErrorException('Failed to retrieve orders');
    }
  }

  @Get(':orderId')
  async getOrderById(@Param('orderId') orderId: string): Promise<any> {
    try {
      return await this.adminOrderTrackingService.getOrderById(orderId);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      console.error('Error in getOrderById:', error);
      throw new InternalServerErrorException('Failed to retrieve order');
    }
  }

  @Post('consume-manually')
  @UseGuards(ApiKeyGuard)
  async consumeOrderManually(
    @Body(new ValidationPipe({ transform: true }))
    orderConsumptionDto: ManualOrderConsumptionDto,
  ): Promise<ManualOrderConsumptionResponseDto> {
    try {
      return await this.adminOrderTrackingService.consumeOrderManually(
        orderConsumptionDto,
      );
    } catch (error) {
      console.error('Error in consumeOrderManually:', error);
      throw new InternalServerErrorException(
        'Failed to consume order manually',
      );
    }
  }

  @Post('update-review-status')
  @UseGuards(ApiKeyGuard)
  async updateOrderReviewStatus(
    @Body(new ValidationPipe({ transform: true }))
    updateOrderReviewDto: UpdateOrderReviewDto,
  ): Promise<UpdateOrderReviewResponseDto> {
    try {
      return await this.adminOrderTrackingService.updateOrderReviewStatus(
        updateOrderReviewDto,
      );
    } catch (error) {
      console.error('Error in updateOrderReviewStatus:', error);
      throw new InternalServerErrorException(
        'Failed to update order review status',
      );
    }
  }
}
